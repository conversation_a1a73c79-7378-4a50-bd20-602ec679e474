//
//  KeyboardEventCapture.swift
//  MousePro
//
//  Created by lincoo on 7/14/25.
//

import Foundation
import Carbon
import Cocoa

/// 键盘事件捕获器
/// 负责监听本机键盘输入并转换为HID协议数据
class KeyboardEventCapture: ObservableObject {

    // MARK: - Properties

    @Published var isCapturing: Bool = false
    @Published var lastKeyEvent: String = ""

    private var eventTap: CFMachPort?
    private var runLoopSource: CFRunLoopSource?
    private weak var virtualHIDManager: VirtualHIDManager?

    // MARK: - Initialization

    init(virtualHIDManager: VirtualHIDManager) {
        self.virtualHIDManager = virtualHIDManager
    }

    deinit {
        stopCapturing()
    }

    // MARK: - Public Methods

    /// 开始捕获键盘事件
    func startCapturing() -> Bool {
        guard !isCapturing else { return true }

        // 请求辅助功能权限
        if !AXIsProcessTrusted() {
            let alert = NSAlert()
            alert.messageText = "需要辅助功能权限"
            alert.informativeText = "请在系统偏好设置 > 安全性与隐私 > 隐私 > 辅助功能中添加此应用"
            alert.addButton(withTitle: "打开系统偏好设置")
            alert.addButton(withTitle: "取消")

            if alert.runModal() == .alertFirstButtonReturn {
                NSWorkspace.shared.open(URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")!)
            }
            return false
        }

        // 创建事件监听器
        let eventMask = (1 << CGEventType.keyDown.rawValue) | (1 << CGEventType.keyUp.rawValue)

        eventTap = CGEvent.tapCreate(
            tap: .cgSessionEventTap,
            place: .headInsertEventTap,
            options: .defaultTap,
            eventsOfInterest: CGEventMask(eventMask),
            callback: keyboardEventCallback,
            userInfo: Unmanaged.passUnretained(self).toOpaque()
        )

        guard let eventTap = eventTap else {
            print("Failed to create event tap")
            return false
        }

        runLoopSource = CFMachPortCreateRunLoopSource(kCFAllocatorDefault, eventTap, 0)
        CFRunLoopAddSource(CFRunLoopGetCurrent(), runLoopSource, .commonModes)
        CGEvent.tapEnable(tap: eventTap, enable: true)

        isCapturing = true
        print("Keyboard capture started")
        return true
    }

    /// 停止捕获键盘事件
    func stopCapturing() {
        guard isCapturing else { return }

        if let eventTap = eventTap {
            CGEvent.tapEnable(tap: eventTap, enable: false)
            CFRunLoopRemoveSource(CFRunLoopGetCurrent(), runLoopSource, .commonModes)
            CFMachPortInvalidate(eventTap)
        }

        eventTap = nil
        runLoopSource = nil
        isCapturing = false
        print("Keyboard capture stopped")
    }

    // MARK: - Event Handling

    func handleKeyboardEvent(_ event: CGEvent) -> CGEvent? {
        let keyCode = event.getIntegerValueField(.keyboardEventKeycode)
        let isKeyDown = event.type == .keyDown

        // 更新UI显示
        DispatchQueue.main.async {
            self.lastKeyEvent = "Key: \(keyCode) \(isKeyDown ? "Down" : "Up")"
        }

        // 转换为HID报告并发送
        if let hidReport = convertToHIDKeyboardReport(keyCode: Int(keyCode), isPressed: isKeyDown) {
            _ = virtualHIDManager?.sendHIDReport(hidReport)
        }

        // 返回事件以继续传播（或返回nil以阻止）
        return event
    }

    // MARK: - HID Protocol Conversion

    private func convertToHIDKeyboardReport(keyCode: Int, isPressed: Bool) -> Data? {
        // HID键盘报告格式：
        // Byte 0: Report ID (通常为0)
        // Byte 1: 修饰键 (Ctrl, Shift, Alt, Cmd)
        // Byte 2: 保留字节 (通常为0)
        // Byte 3-8: 按键码 (最多6个同时按下的键)

        var report = Data(count: 9) // 9字节报告
        report[0] = 0x01 // Report ID

        // 将macOS键码转换为HID键码
        if let hidKeyCode = macOSToHIDKeyCode(keyCode) {
            if isPressed {
                // 按键按下
                report[3] = hidKeyCode
            } else {
                // 按键释放 - 发送空报告
                // report已经初始化为0，所以不需要额外操作
            }
        }

        return report
    }

    private func macOSToHIDKeyCode(_ macOSKeyCode: Int) -> UInt8? {
        // macOS键码到HID键码的映射表
        let keyCodeMap: [Int: UInt8] = [
            // 字母键
            0: 0x04,   // A
            11: 0x05,  // B
            8: 0x06,   // C
            2: 0x07,   // D
            14: 0x08,  // E
            3: 0x09,   // F
            5: 0x0A,   // G
            4: 0x0B,   // H
            34: 0x0C,  // I
            38: 0x0D,  // J
            40: 0x0E,  // K
            37: 0x0F,  // L
            46: 0x10,  // M
            45: 0x11,  // N
            31: 0x12,  // O
            35: 0x13,  // P
            12: 0x14,  // Q
            15: 0x15,  // R
            1: 0x16,   // S
            17: 0x17,  // T
            32: 0x18,  // U
            9: 0x19,   // V
            13: 0x1A,  // W
            7: 0x1B,   // X
            16: 0x1C,  // Y
            6: 0x1D,   // Z

            // 数字键
            29: 0x1E,  // 1
            18: 0x1F,  // 2
            19: 0x20,  // 3
            20: 0x21,  // 4
            21: 0x22,  // 5
            23: 0x23,  // 6
            22: 0x24,  // 7
            26: 0x25,  // 8
            28: 0x26,  // 9
            25: 0x27,  // 0

            // 功能键
            36: 0x28,  // Return
            53: 0x29,  // Escape
            51: 0x2A,  // Backspace
            48: 0x2B,  // Tab
            49: 0x2C,  // Space

            // 方向键
            126: 0x52, // Up Arrow
            125: 0x51, // Down Arrow
            123: 0x50, // Left Arrow
            124: 0x4F, // Right Arrow
        ]

        return keyCodeMap[macOSKeyCode]
    }
}

// MARK: - C Callback

private func keyboardEventCallback(
    proxy: CGEventTapProxy,
    type: CGEventType,
    event: CGEvent,
    refcon: UnsafeMutableRawPointer?
) -> Unmanaged<CGEvent>? {

    guard let refcon = refcon else { return Unmanaged.passUnretained(event) }

    let capture = Unmanaged<KeyboardEventCapture>.fromOpaque(refcon).takeUnretainedValue()

    if let modifiedEvent = capture.handleKeyboardEvent(event) {
        return Unmanaged.passUnretained(modifiedEvent)
    }

    return nil
}