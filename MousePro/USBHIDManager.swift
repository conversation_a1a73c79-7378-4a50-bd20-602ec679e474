//
//  VirtualHIDManager.swift
//  MousePro
//
//  Created by lincoo on 7/14/25.
//

import Foundation
import IOKit
import IOKit.hid
import CoreGraphics

/// 虚拟HID设备管理器
/// 在macOS上使用CGEvent来模拟鼠标输入
class VirtualHIDManager: ObservableObject {

    // MARK: - Properties

    @Published var isVirtualDeviceActive: Bool = false
    @Published var connectionStatus: String = "未创建虚拟设备"
    @Published var lastReportSent: String = ""

    private var eventSource: CGEventSource?

    // MARK: - Initialization

    init() {
        // 初始化时不自动创建设备
    }

    deinit {
        destroyVirtualDevice()
    }

    // MARK: - Public Methods

    /// 创建虚拟HID鼠标设备
    func createVirtualDevice() -> Bool {
        guard eventSource == nil else {
            print("Virtual device already exists")
            return true
        }

        // 创建CGEventSource作为虚拟设备
        eventSource = CGEventSource(stateID: .hidSystemState)

        guard eventSource != nil else {
            print("Failed to create virtual HID device")
            updateConnectionStatus("创建虚拟设备失败")
            return false
        }

        isVirtualDeviceActive = true
        updateConnectionStatus("虚拟鼠标设备已创建")
        print("Virtual HID mouse device created successfully")
        return true
    }

    /// 销毁虚拟设备
    func destroyVirtualDevice() {
        if eventSource != nil {
            eventSource = nil
            isVirtualDeviceActive = false
            updateConnectionStatus("虚拟设备已销毁")
            print("Virtual HID device destroyed")
        }
    }

    /// 发送HID报告到虚拟设备
    func sendHIDReport(_ report: Data) -> Bool {
        guard let source = eventSource else {
            print("No virtual device available")
            return false
        }

        guard report.count >= 4 else {
            print("Invalid report size")
            return false
        }

        // 解析HID报告
        let buttonMask = report[0]
        let deltaX = Int8(bitPattern: report[1])
        let deltaY = Int8(bitPattern: report[2])
        let scrollDelta = Int8(bitPattern: report[3])

        var success = true

        // 处理鼠标移动
        if deltaX != 0 || deltaY != 0 {
            if let moveEvent = CGEvent(mouseEventSource: source, mouseType: .mouseMoved, mouseCursorPosition: CGPoint.zero, mouseButton: .left) {
                moveEvent.setIntegerValueField(.mouseEventDeltaX, value: Int64(deltaX))
                moveEvent.setIntegerValueField(.mouseEventDeltaY, value: Int64(deltaY))
                moveEvent.post(tap: .cghidEventTap)
            } else {
                success = false
            }
        }

        // 处理滚轮
        if scrollDelta != 0 {
            if let scrollEvent = CGEvent(scrollWheelEvent2Source: source, units: .pixel, wheelCount: 1, wheel1: Int32(scrollDelta), wheel2: 0, wheel3: 0) {
                scrollEvent.post(tap: .cghidEventTap)
            } else {
                success = false
            }
        }

        // 处理按键（这里简化处理，实际需要维护按键状态）
        // 注意：这种方法会产生真实的鼠标事件，可能会被原始事件捕获器捕获

        if success {
            DispatchQueue.main.async {
                self.lastReportSent = "Report sent: \(report.map { String(format: "%02X", $0) }.joined(separator: " "))"
            }
        } else {
            print("Failed to send HID report")
        }

        return success
    }

    // MARK: - Private Methods

    private func updateConnectionStatus(_ status: String) {
        DispatchQueue.main.async {
            self.connectionStatus = status
        }
    }
}

// MARK: - Virtual Device Model

struct VirtualMouseDevice {
    let id = UUID()
    let name: String = "Virtual Mouse"
    let type: String = "Virtual HID Device"
    let isActive: Bool

    var description: String {
        return "\(name) (\(type)) - \(isActive ? "Active" : "Inactive")"
    }
}

