//
//  ContentView.swift
//  MousePro
//
//  Created by lincoo on 7/14/25.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var virtualHIDManager = VirtualHIDManager()
    @StateObject private var virtualDisplayManager: VirtualDisplayManager
    @StateObject private var keyboardCapture: KeyboardEventCapture
    @StateObject private var mouseCapture: MouseEventCapture

    @State private var isCapturingEnabled = false
    @State private var useVirtualDisplay = true

    init() {
        let virtualHIDManager = VirtualHIDManager()
        self._virtualHIDManager = StateObject(wrappedValue: virtualHIDManager)
        self._virtualDisplayManager = StateObject(wrappedValue: VirtualDisplayManager(virtualHIDManager: virtualHIDManager))
        self._keyboardCapture = StateObject(wrappedValue: KeyboardEventCapture(virtualHIDManager: virtualHIDManager))
        self._mouseCapture = StateObject(wrappedValue: MouseEventCapture(virtualHIDManager: virtualHIDManager))
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                HStack {
                    Image(systemName: "keyboard")
                        .font(.largeTitle)
                        .foregroundColor(.blue)
                    Text("MousePro")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                }
                .padding(.top)

                // 虚拟设备状态
                VStack(alignment: .leading, spacing: 10) {
                    Text("虚拟设备状态")
                        .font(.headline)

                    HStack {
                        Circle()
                            .fill(virtualHIDManager.isVirtualDeviceActive ? Color.green : Color.red)
                            .frame(width: 12, height: 12)
                        Text(virtualHIDManager.connectionStatus)
                            .foregroundColor(.secondary)
                    }

                    if !virtualHIDManager.lastReportSent.isEmpty {
                        Text("最后发送: \(virtualHIDManager.lastReportSent)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)

                // 虚拟显示屏管理
                VStack(alignment: .leading, spacing: 10) {
                    Text("虚拟显示屏")
                        .font(.headline)

                    HStack {
                        VStack(alignment: .leading) {
                            Text("虚拟显示屏")
                                .font(.subheadline)
                                .fontWeight(.medium)
                            Text(virtualDisplayManager.displayStatus)
                                .font(.caption)
                                .foregroundColor(.secondary)
                            if virtualDisplayManager.isVirtualDisplayActive {
                                Text("鼠标位置: (\(Int(virtualDisplayManager.mousePosition.x)), \(Int(virtualDisplayManager.mousePosition.y)))")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }

                        Spacer()

                        Button(virtualDisplayManager.isVirtualDisplayActive ? "关闭显示屏" : "打开显示屏") {
                            if virtualDisplayManager.isVirtualDisplayActive {
                                virtualDisplayManager.closeVirtualDisplay()
                            } else {
                                _ = virtualDisplayManager.createVirtualDisplay()
                            }
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .padding()
                    .background(virtualDisplayManager.isVirtualDisplayActive ? Color.blue.opacity(0.1) : Color.clear)
                    .cornerRadius(8)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)

                // 虚拟HID设备管理
                VStack(alignment: .leading, spacing: 10) {
                    Text("虚拟HID设备")
                        .font(.headline)

                    let virtualDevice = VirtualMouseDevice(isActive: virtualHIDManager.isVirtualDeviceActive)

                    HStack {
                        VStack(alignment: .leading) {
                            Text(virtualDevice.name)
                                .font(.subheadline)
                                .fontWeight(.medium)
                            Text(virtualDevice.description)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        Button(virtualHIDManager.isVirtualDeviceActive ? "销毁设备" : "创建设备") {
                            if virtualHIDManager.isVirtualDeviceActive {
                                virtualHIDManager.destroyVirtualDevice()
                            } else {
                                _ = virtualHIDManager.createVirtualDevice()
                            }
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .padding()
                    .background(virtualHIDManager.isVirtualDeviceActive ? Color.blue.opacity(0.1) : Color.clear)
                    .cornerRadius(8)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)

                // 控制按钮
                VStack(spacing: 15) {
                    HStack(spacing: 15) {
                        Toggle("使用虚拟显示屏", isOn: $useVirtualDisplay)
                            .toggleStyle(SwitchToggleStyle())

                        Spacer()

                        Button(isCapturingEnabled ? "停止捕获" : "开始捕获") {
                            toggleCapturing()
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(!virtualHIDManager.isVirtualDeviceActive)
                    }
                }

                // 事件信息
                if isCapturingEnabled {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("实时事件")
                            .font(.headline)

                        VStack(alignment: .leading, spacing: 5) {
                            if useVirtualDisplay {
                                Text("虚拟显示屏: \(virtualDisplayManager.lastMouseEvent)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Text("虚拟位置: (\(Int(virtualDisplayManager.mousePosition.x)), \(Int(virtualDisplayManager.mousePosition.y)))")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            } else {
                                Text("键盘: \(keyboardCapture.lastKeyEvent)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Text("鼠标: \(mouseCapture.lastMouseEvent)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Text("位置: (\(Int(mouseCapture.mousePosition.x)), \(Int(mouseCapture.mousePosition.y)))")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Text("HID报告: \(virtualHIDManager.lastReportSent)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)
                }

                Spacer()
            }
            .padding()
            .navigationTitle("MousePro")
        }
        .onAppear {
            // 自动创建虚拟设备
            _ = virtualHIDManager.createVirtualDevice()
        }
        .onDisappear {
            if isCapturingEnabled {
                stopCapturing()
            }
            virtualHIDManager.destroyVirtualDevice()
            virtualDisplayManager.closeVirtualDisplay()
        }
    }

    private func toggleCapturing() {
        if isCapturingEnabled {
            stopCapturing()
        } else {
            startCapturing()
        }
    }

    private func startCapturing() {
        if useVirtualDisplay {
            // 使用虚拟显示屏模式，不需要全局事件捕获
            if virtualDisplayManager.isVirtualDisplayActive {
                isCapturingEnabled = true
            } else {
                // 如果虚拟显示屏未激活，先激活它
                if virtualDisplayManager.createVirtualDisplay() {
                    isCapturingEnabled = true
                }
            }
        } else {
            // 使用传统的全局事件捕获模式
            let keyboardSuccess = keyboardCapture.startCapturing()
            let mouseSuccess = mouseCapture.startCapturing()

            if keyboardSuccess && mouseSuccess {
                isCapturingEnabled = true
            }
        }
    }

    private func stopCapturing() {
        if !useVirtualDisplay {
            keyboardCapture.stopCapturing()
            mouseCapture.stopCapturing()
        }
        isCapturingEnabled = false
    }
}

#Preview {
    ContentView()
}
