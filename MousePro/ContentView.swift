//
//  ContentView.swift
//  MousePro
//
//  Created by lincoo on 7/14/25.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var virtualHIDManager = VirtualHIDManager()
    @StateObject private var keyboardCapture: KeyboardEventCapture
    @StateObject private var mouseCapture: MouseEventCapture

    @State private var isCapturingEnabled = false

    init() {
        let virtualHIDManager = VirtualHIDManager()
        self._virtualHIDManager = StateObject(wrappedValue: virtualHIDManager)
        self._keyboardCapture = StateObject(wrappedValue: KeyboardEventCapture(virtualHIDManager: virtualHIDManager))
        self._mouseCapture = StateObject(wrappedValue: MouseEventCapture(virtualHIDManager: virtualHIDManager))
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                HStack {
                    Image(systemName: "keyboard")
                        .font(.largeTitle)
                        .foregroundColor(.blue)
                    Text("MousePro")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                }
                .padding(.top)

                // 虚拟设备状态
                VStack(alignment: .leading, spacing: 10) {
                    Text("虚拟设备状态")
                        .font(.headline)

                    HStack {
                        Circle()
                            .fill(virtualHIDManager.isVirtualDeviceActive ? Color.green : Color.red)
                            .frame(width: 12, height: 12)
                        Text(virtualHIDManager.connectionStatus)
                            .foregroundColor(.secondary)
                    }

                    if !virtualHIDManager.lastReportSent.isEmpty {
                        Text("最后发送: \(virtualHIDManager.lastReportSent)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)

                // 虚拟设备管理
                VStack(alignment: .leading, spacing: 10) {
                    Text("虚拟HID设备")
                        .font(.headline)

                    let virtualDevice = VirtualMouseDevice(isActive: virtualHIDManager.isVirtualDeviceActive)

                    HStack {
                        VStack(alignment: .leading) {
                            Text(virtualDevice.name)
                                .font(.subheadline)
                                .fontWeight(.medium)
                            Text(virtualDevice.description)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        Button(virtualHIDManager.isVirtualDeviceActive ? "销毁设备" : "创建设备") {
                            if virtualHIDManager.isVirtualDeviceActive {
                                virtualHIDManager.destroyVirtualDevice()
                            } else {
                                _ = virtualHIDManager.createVirtualDevice()
                            }
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .padding()
                    .background(virtualHIDManager.isVirtualDeviceActive ? Color.blue.opacity(0.1) : Color.clear)
                    .cornerRadius(8)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)

                // 控制按钮
                VStack(spacing: 15) {
                    Button(isCapturingEnabled ? "停止捕获" : "开始捕获") {
                        toggleCapturing()
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(!virtualHIDManager.isVirtualDeviceActive)
                }

                // 事件信息
                if isCapturingEnabled {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("实时事件")
                            .font(.headline)

                        VStack(alignment: .leading, spacing: 5) {
                            Text("键盘: \(keyboardCapture.lastKeyEvent)")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Text("鼠标: \(mouseCapture.lastMouseEvent)")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Text("位置: (\(Int(mouseCapture.mousePosition.x)), \(Int(mouseCapture.mousePosition.y)))")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)
                }

                Spacer()
            }
            .padding()
            .navigationTitle("MousePro")
        }
        .onAppear {
            // 自动创建虚拟设备
            _ = virtualHIDManager.createVirtualDevice()
        }
        .onDisappear {
            if isCapturingEnabled {
                stopCapturing()
            }
            virtualHIDManager.destroyVirtualDevice()
        }
    }

    private func toggleCapturing() {
        if isCapturingEnabled {
            stopCapturing()
        } else {
            startCapturing()
        }
    }

    private func startCapturing() {
        let keyboardSuccess = keyboardCapture.startCapturing()
        let mouseSuccess = mouseCapture.startCapturing()

        if keyboardSuccess && mouseSuccess {
            isCapturingEnabled = true
        }
    }

    private func stopCapturing() {
        keyboardCapture.stopCapturing()
        mouseCapture.stopCapturing()
        isCapturingEnabled = false
    }
}

#Preview {
    ContentView()
}
