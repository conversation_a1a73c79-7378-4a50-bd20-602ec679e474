//
//  VirtualDisplayManager.swift
//  MousePro
//
//  Created by lincoo on 7/14/25.
//

import Foundation
import SwiftUI
import AppKit

/// 虚拟显示屏管理器
/// 创建和管理虚拟显示屏窗口
class VirtualDisplayManager: ObservableObject {
    
    // MARK: - Properties
    
    @Published var isVirtualDisplayActive: Bool = false
    @Published var displayStatus: String = "虚拟显示屏未激活"
    @Published var displaySize: CGSize = CGSize(width: 1024, height: 768)
    @Published var mousePosition: CGPoint = .zero
    @Published var lastMouseEvent: String = ""
    
    private var virtualDisplayWindow: VirtualDisplayWindow?
    private weak var virtualHIDManager: VirtualHIDManager?
    
    // MARK: - Initialization
    
    init(virtualHIDManager: VirtualHIDManager) {
        self.virtualHIDManager = virtualHIDManager
    }
    
    deinit {
        closeVirtualDisplay()
    }
    
    // MARK: - Public Methods
    
    /// 创建并显示虚拟显示屏
    func createVirtualDisplay() -> Bool {
        guard virtualDisplayWindow == nil else {
            print("Virtual display already exists")
            return true
        }
        
        // 创建虚拟显示屏窗口
        virtualDisplayWindow = VirtualDisplayWindow(
            size: displaySize,
            displayManager: self
        )
        
        guard let window = virtualDisplayWindow else {
            updateDisplayStatus("创建虚拟显示屏失败")
            return false
        }
        
        // 显示窗口
        window.makeKeyAndOrderFront(nil)
        window.center()
        
        isVirtualDisplayActive = true
        updateDisplayStatus("虚拟显示屏已激活 (\(Int(displaySize.width))x\(Int(displaySize.height)))")
        print("Virtual display created successfully")
        return true
    }
    
    /// 关闭虚拟显示屏
    func closeVirtualDisplay() {
        if let window = virtualDisplayWindow {
            window.close()
            virtualDisplayWindow = nil
            isVirtualDisplayActive = false
            updateDisplayStatus("虚拟显示屏已关闭")
            print("Virtual display closed")
        }
    }
    
    /// 设置虚拟显示屏大小
    func setDisplaySize(_ size: CGSize) {
        displaySize = size
        if let window = virtualDisplayWindow {
            window.setContentSize(size)
            updateDisplayStatus("虚拟显示屏已激活 (\(Int(size.width))x\(Int(size.height)))")
        }
    }
    
    /// 处理虚拟显示屏中的鼠标事件
    func handleVirtualMouseEvent(_ event: NSEvent, in view: NSView) {
        let location = view.convert(event.locationInWindow, from: nil)
        
        // 更新鼠标位置
        DispatchQueue.main.async {
            self.mousePosition = location
        }
        
        switch event.type {
        case .leftMouseDown, .leftMouseUp:
            handleVirtualMouseButton(button: 1, isPressed: event.type == .leftMouseDown, at: location)
            
        case .rightMouseDown, .rightMouseUp:
            handleVirtualMouseButton(button: 2, isPressed: event.type == .rightMouseDown, at: location)
            
        case .otherMouseDown, .otherMouseUp:
            handleVirtualMouseButton(button: 3, isPressed: event.type == .otherMouseDown, at: location)
            
        case .mouseMoved, .leftMouseDragged, .rightMouseDragged, .otherMouseDragged:
            handleVirtualMouseMovement(to: location)
            
        case .scrollWheel:
            handleVirtualScrollWheel(deltaX: event.scrollingDeltaX, deltaY: event.scrollingDeltaY)
            
        default:
            break
        }
    }
    
    // MARK: - Private Methods
    
    private func updateDisplayStatus(_ status: String) {
        DispatchQueue.main.async {
            self.displayStatus = status
        }
    }
    
    private func handleVirtualMouseButton(button: Int, isPressed: Bool, at location: CGPoint) {
        DispatchQueue.main.async {
            self.lastMouseEvent = "Button \(button) \(isPressed ? "Down" : "Up") at (\(Int(location.x)), \(Int(location.y)))"
        }
        
        if let hidReport = convertToHIDMouseButtonReport(button: button, isPressed: isPressed) {
            _ = virtualHIDManager?.sendHIDReport(hidReport)
        }
    }
    
    private func handleVirtualMouseMovement(to location: CGPoint) {
        // 计算相对移动（简化处理）
        let deltaX = Int(location.x - mousePosition.x)
        let deltaY = Int(location.y - mousePosition.y)
        
        DispatchQueue.main.async {
            self.lastMouseEvent = "Move to (\(Int(location.x)), \(Int(location.y)))"
        }
        
        if let hidReport = convertToHIDMouseMovementReport(deltaX: deltaX, deltaY: deltaY) {
            _ = virtualHIDManager?.sendHIDReport(hidReport)
        }
    }
    
    private func handleVirtualScrollWheel(deltaX: Double, deltaY: Double) {
        DispatchQueue.main.async {
            self.lastMouseEvent = "Scroll (\(deltaX), \(deltaY))"
        }
        
        if let hidReport = convertToHIDScrollReport(deltaX: Int(deltaX), deltaY: Int(deltaY)) {
            _ = virtualHIDManager?.sendHIDReport(hidReport)
        }
    }
    
    // MARK: - HID Protocol Conversion
    
    private func convertToHIDMouseButtonReport(button: Int, isPressed: Bool) -> Data? {
        var report = Data(count: 4)
        
        var buttonMask: UInt8 = 0
        if isPressed {
            switch button {
            case 1: buttonMask |= 0x01 // 左键
            case 2: buttonMask |= 0x02 // 右键
            case 3: buttonMask |= 0x04 // 中键
            default: break
            }
        }
        
        report[0] = buttonMask
        report[1] = 0 // X轴移动
        report[2] = 0 // Y轴移动
        report[3] = 0 // 滚轮
        return report
    }
    
    private func convertToHIDMouseMovementReport(deltaX: Int, deltaY: Int) -> Data? {
        var report = Data(count: 4)
        
        let clampedDeltaX = max(-127, min(127, deltaX))
        let clampedDeltaY = max(-127, min(127, deltaY))
        
        report[0] = 0 // 按键状态
        report[1] = UInt8(bitPattern: Int8(clampedDeltaX)) // X轴相对移动
        report[2] = UInt8(bitPattern: Int8(clampedDeltaY)) // Y轴相对移动
        report[3] = 0 // 滚轮
        
        return report
    }
    
    private func convertToHIDScrollReport(deltaX: Int, deltaY: Int) -> Data? {
        var report = Data(count: 4)
        
        let clampedDeltaY = max(-127, min(127, deltaY))
        
        report[0] = 0 // 按键状态
        report[1] = 0 // X轴移动
        report[2] = 0 // Y轴移动
        report[3] = UInt8(bitPattern: Int8(clampedDeltaY)) // 滚轮
        
        return report
    }
}

// MARK: - Virtual Display Window

class VirtualDisplayWindow: NSWindow {
    
    private weak var displayManager: VirtualDisplayManager?
    
    init(size: CGSize, displayManager: VirtualDisplayManager) {
        self.displayManager = displayManager
        
        let contentRect = NSRect(origin: .zero, size: size)
        
        super.init(
            contentRect: contentRect,
            styleMask: [.titled, .closable, .miniaturizable, .resizable],
            backing: .buffered,
            defer: false
        )
        
        setupWindow()
    }
    
    private func setupWindow() {
        title = "MousePro 虚拟显示屏"
        backgroundColor = NSColor.black
        isOpaque = true
        hasShadow = true
        
        // 创建内容视图
        let contentView = VirtualDisplayView(displayManager: displayManager)
        self.contentView = contentView
        
        // 设置窗口代理
        delegate = self
    }
}

// MARK: - Virtual Display View

class VirtualDisplayView: NSView {
    
    private weak var displayManager: VirtualDisplayManager?
    
    init(displayManager: VirtualDisplayManager?) {
        self.displayManager = displayManager
        super.init(frame: .zero)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupView() {
        wantsLayer = true
        layer?.backgroundColor = NSColor.darkGray.cgColor
        
        // 添加网格背景
        addGridBackground()
        
        // 添加中心十字线
        addCenterCrosshair()
    }
    
    private func addGridBackground() {
        // 这里可以添加网格背景的绘制逻辑
    }
    
    private func addCenterCrosshair() {
        // 这里可以添加中心十字线的绘制逻辑
    }
    
    override func draw(_ dirtyRect: NSRect) {
        super.draw(dirtyRect)
        
        // 绘制网格
        NSColor.gray.withAlphaComponent(0.3).setStroke()
        let gridSize: CGFloat = 50
        let path = NSBezierPath()
        
        // 垂直线
        var x: CGFloat = 0
        while x <= bounds.width {
            path.move(to: NSPoint(x: x, y: 0))
            path.line(to: NSPoint(x: x, y: bounds.height))
            x += gridSize
        }
        
        // 水平线
        var y: CGFloat = 0
        while y <= bounds.height {
            path.move(to: NSPoint(x: 0, y: y))
            path.line(to: NSPoint(x: bounds.width, y: y))
            y += gridSize
        }
        
        path.lineWidth = 1.0
        path.stroke()
        
        // 绘制中心十字线
        NSColor.red.withAlphaComponent(0.7).setStroke()
        let centerPath = NSBezierPath()
        let center = NSPoint(x: bounds.width / 2, y: bounds.height / 2)
        
        // 水平线
        centerPath.move(to: NSPoint(x: center.x - 20, y: center.y))
        centerPath.line(to: NSPoint(x: center.x + 20, y: center.y))
        
        // 垂直线
        centerPath.move(to: NSPoint(x: center.x, y: center.y - 20))
        centerPath.line(to: NSPoint(x: center.x, y: center.y + 20))
        
        centerPath.lineWidth = 2.0
        centerPath.stroke()
    }
    
    // MARK: - Mouse Event Handling
    
    override func mouseDown(with event: NSEvent) {
        displayManager?.handleVirtualMouseEvent(event, in: self)
    }
    
    override func mouseUp(with event: NSEvent) {
        displayManager?.handleVirtualMouseEvent(event, in: self)
    }
    
    override func rightMouseDown(with event: NSEvent) {
        displayManager?.handleVirtualMouseEvent(event, in: self)
    }
    
    override func rightMouseUp(with event: NSEvent) {
        displayManager?.handleVirtualMouseEvent(event, in: self)
    }
    
    override func otherMouseDown(with event: NSEvent) {
        displayManager?.handleVirtualMouseEvent(event, in: self)
    }
    
    override func otherMouseUp(with event: NSEvent) {
        displayManager?.handleVirtualMouseEvent(event, in: self)
    }
    
    override func mouseMoved(with event: NSEvent) {
        displayManager?.handleVirtualMouseEvent(event, in: self)
    }
    
    override func mouseDragged(with event: NSEvent) {
        displayManager?.handleVirtualMouseEvent(event, in: self)
    }
    
    override func rightMouseDragged(with event: NSEvent) {
        displayManager?.handleVirtualMouseEvent(event, in: self)
    }
    
    override func otherMouseDragged(with event: NSEvent) {
        displayManager?.handleVirtualMouseEvent(event, in: self)
    }
    
    override func scrollWheel(with event: NSEvent) {
        displayManager?.handleVirtualMouseEvent(event, in: self)
    }
    
    override var acceptsFirstResponder: Bool {
        return true
    }
    
    override func updateTrackingAreas() {
        super.updateTrackingAreas()
        
        // 移除旧的跟踪区域
        for trackingArea in trackingAreas {
            removeTrackingArea(trackingArea)
        }
        
        // 添加新的跟踪区域
        let trackingArea = NSTrackingArea(
            rect: bounds,
            options: [.activeInKeyWindow, .mouseMoved, .enabledDuringMouseDrag],
            owner: self,
            userInfo: nil
        )
        addTrackingArea(trackingArea)
    }
}

// MARK: - Window Delegate

extension VirtualDisplayWindow: NSWindowDelegate {
    
    func windowWillClose(_ notification: Notification) {
        displayManager?.closeVirtualDisplay()
    }
    
    func windowDidResize(_ notification: Notification) {
        if let size = contentView?.frame.size {
            displayManager?.setDisplaySize(size)
        }
    }
}
